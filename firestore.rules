rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection
    match /users/{userId} {
      // Allow creating user profiles without authentication (for initial signup)
      allow create: if true;

      // Users can read and write their own profile
      allow read, write: if request.auth != null && request.auth.uid == userId;

      // Users can read other users' public profile data
      allow read: if request.auth != null;

      // Allow updates for profile completion
      allow update: if request.auth != null &&
                   request.auth.uid == userId &&
                   !('uid' in request.resource.data) &&
                   !('createdAt' in request.resource.data);

      // Temporary: Allow all operations for development
      allow read, write: if true;
    }

    // Chats collection
    match /chats/{chatId} {
      // Users can read chats they participate in
      allow read: if request.auth != null && 
                 request.auth.uid in resource.data.participants;
      
      // Users can create chats
      allow create: if request.auth != null && 
                   request.auth.uid in request.resource.data.participants;
      
      // Users can update chats they participate in
      allow update: if request.auth != null && 
                   request.auth.uid in resource.data.participants;
      
      // Only admins can delete group chats
      allow delete: if request.auth != null && 
                   (resource.data.adminIds == null || 
                    request.auth.uid in resource.data.adminIds);
    }

    // Messages collection
    match /messages/{messageId} {
      // Users can read messages from chats they participate in
      allow read: if request.auth != null && 
                 isParticipantInChat(request.auth.uid, resource.data.chatId);
      
      // Users can create messages in chats they participate in
      allow create: if request.auth != null && 
                   request.auth.uid == request.resource.data.senderId &&
                   isParticipantInChat(request.auth.uid, request.resource.data.chatId);
      
      // Users can update their own messages (for editing, reactions, etc.)
      allow update: if request.auth != null && 
                   (request.auth.uid == resource.data.senderId ||
                    isParticipantInChat(request.auth.uid, resource.data.chatId));
      
      // Users can delete their own messages
      allow delete: if request.auth != null && 
                   request.auth.uid == resource.data.senderId;
    }

    // Contacts collection
    match /contacts/{contactId} {
      // Users can read and write their own contacts
      allow read, write: if request.auth != null && 
                        request.auth.uid == resource.data.userId;
      
      // Users can create contacts for themselves
      allow create: if request.auth != null && 
                   request.auth.uid == request.resource.data.userId;
    }

    // Payment transactions collection
    match /transactions/{transactionId} {
      // Users can read transactions they're involved in
      allow read: if request.auth != null && 
                 (request.auth.uid == resource.data.senderId ||
                  request.auth.uid == resource.data.receiverId);
      
      // Users can create transactions they're sending
      allow create: if request.auth != null && 
                   request.auth.uid == request.resource.data.senderId;
      
      // Only system can update transaction status
      allow update: if false; // Handled by cloud functions
    }

    // User sessions collection (for authentication)
    match /sessions/{sessionId} {
      allow read, write: if request.auth != null && 
                        request.auth.uid == resource.data.userId;
    }

    // Helper function to check if user is participant in chat
    function isParticipantInChat(userId, chatId) {
      return exists(/databases/$(database)/documents/chats/$(chatId)) &&
             userId in get(/databases/$(database)/documents/chats/$(chatId)).data.participants;
    }

    // Test collection (remove in production)
    match /test/{document} {
      allow read, write: if true;
    }

    // Block access to all other collections
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
