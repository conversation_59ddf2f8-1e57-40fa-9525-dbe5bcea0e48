import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Check Twilio configuration
    const twilioConfig = {
      accountSid: process.env.TWILIO_ACCOUNT_SID ? '✅ Set' : '❌ Missing',
      authToken: process.env.TWILIO_AUTH_TOKEN ? '✅ Set' : '❌ Missing',
      phoneNumber: process.env.TWILIO_PHONE_NUMBER ? '✅ Set' : '❌ Missing'
    };

    const actualValues = {
      accountSid: process.env.TWILIO_ACCOUNT_SID,
      authToken: process.env.TWILIO_AUTH_TOKEN ? '***' + process.env.TWILIO_AUTH_TOKEN.slice(-4) : 'Not set',
      phoneNumber: process.env.TWILIO_PHONE_NUMBER
    };

    return NextResponse.json({
      success: true,
      message: 'Twilio configuration check',
      config: twilioConfig,
      actualValues,
      nodeEnv: process.env.NODE_ENV,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('Twilio config test error:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to check Twilio configuration',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { phoneNumber } = await request.json();

    if (!phoneNumber) {
      return NextResponse.json(
        { error: 'Phone number is required' },
        { status: 400 }
      );
    }

    // Test Twilio SMS sending
    if (process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN) {
      const twilio = require('twilio')(
        process.env.TWILIO_ACCOUNT_SID,
        process.env.TWILIO_AUTH_TOKEN
      );

      const testMessage = await twilio.messages.create({
        body: 'Test message from BoGuani! Your SMS integration is working.',
        from: process.env.TWILIO_PHONE_NUMBER,
        to: phoneNumber
      });

      return NextResponse.json({
        success: true,
        message: 'Test SMS sent successfully',
        messageSid: testMessage.sid,
        to: phoneNumber
      });

    } else {
      return NextResponse.json({
        success: false,
        error: 'Twilio credentials not configured'
      }, { status: 500 });
    }

  } catch (error: any) {
    console.error('Twilio test SMS error:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to send test SMS',
      details: error.code || 'Unknown error'
    }, { status: 500 });
  }
}
