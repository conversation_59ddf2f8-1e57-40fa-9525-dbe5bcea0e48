#!/bin/bash

# BoGuani Heroku Deployment Script
echo "🚀 Deploying BoGuani to Heroku..."

# Check if Heroku CLI is installed
if ! command -v heroku &> /dev/null; then
    echo "❌ Heroku CLI not found. Please install it first:"
    echo "   https://devcenter.heroku.com/articles/heroku-cli"
    exit 1
fi

# Check if user is logged in to Hero<PERSON>
if ! heroku auth:whoami &> /dev/null; then
    echo "🔐 Please login to <PERSON><PERSON> first:"
    heroku login
fi

# Get app name from user
read -p "Enter your Heroku app name (or press Enter for 'boguani-messenger'): " APP_NAME
APP_NAME=${APP_NAME:-boguani-messenger}

echo "📱 Creating Heroku app: $APP_NAME"

# Create Heroku app
heroku create $APP_NAME

# Set buildpack
heroku buildpacks:set heroku/nodejs --app $APP_NAME

echo "⚙️  Setting environment variables..."

# Set environment variables
heroku config:set \
  NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyBqMCY_vblZIApBW5I6aShR1iVQIUtnXJ0 \
  NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=chatpay-4922e.firebaseapp.com \
  NEXT_PUBLIC_FIREBASE_PROJECT_ID=chatpay-4922e \
  NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=chatpay-4922e.appspot.com \
  NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=211600276697 \
  NEXT_PUBLIC_FIREBASE_APP_ID=1:211600276697:web:a94ed4b6baaf7a654492c8 \
  NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=G-GGBK4R7EFV \
  NODE_ENV=production \
  RATE_LIMIT_MAX=100 \
  RATE_LIMIT_WINDOW=900000 \
  --app $APP_NAME

# Generate secure keys
echo "🔐 Generating secure keys..."
JWT_SECRET=$(openssl rand -base64 32)
NEXTAUTH_SECRET=$(openssl rand -base64 32)
ENCRYPTION_KEY=$(openssl rand -base64 32)
MESSAGE_ENCRYPTION_KEY=$(openssl rand -base64 32)
PLAID_ENCRYPTION_KEY=$(openssl rand -base64 32)

heroku config:set \
  JWT_SECRET="$JWT_SECRET" \
  NEXTAUTH_SECRET="$NEXTAUTH_SECRET" \
  ENCRYPTION_KEY="$ENCRYPTION_KEY" \
  MESSAGE_ENCRYPTION_KEY="$MESSAGE_ENCRYPTION_KEY" \
  PLAID_ENCRYPTION_KEY="$PLAID_ENCRYPTION_KEY" \
  --app $APP_NAME

# Set app URL
heroku config:set NEXT_PUBLIC_APP_URL=https://$APP_NAME.herokuapp.com --app $APP_NAME

echo "📦 Deploying to Heroku..."

# Add git remote if it doesn't exist
if ! git remote get-url heroku &> /dev/null; then
    heroku git:remote -a $APP_NAME
fi

# Deploy
git add .
git commit -m "Deploy BoGuani to Heroku" || echo "No changes to commit"
git push heroku main

echo "✅ Deployment complete!"
echo "🌐 Your app is available at: https://$APP_NAME.herokuapp.com"
echo ""
echo "📋 Next steps:"
echo "1. Go to Firebase Console and add your Heroku domain to authorized domains"
echo "2. Enable Phone authentication in Firebase"
echo "3. Set up Plaid credentials if needed:"
echo "   heroku config:set PLAID_CLIENT_ID=your_client_id --app $APP_NAME"
echo "   heroku config:set PLAID_SECRET=your_secret --app $APP_NAME"
echo ""
echo "🎉 BoGuani is now live on Heroku!"

# Open the app
read -p "Open the app in browser? (y/n): " OPEN_APP
if [[ $OPEN_APP =~ ^[Yy]$ ]]; then
    heroku open --app $APP_NAME
fi
