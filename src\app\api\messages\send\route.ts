import { NextRequest, NextResponse } from 'next/server';
import { firestoreService } from '@/lib/firestore';
import { rateLimit } from '@/lib/rate-limit';

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const rateLimitResult = await rateLimit(request, 50, 60000); // 50 messages per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        { error: 'Too many messages. Please slow down.' },
        { status: 429 }
      );
    }

    const { chatId, senderId, text, type = 'text', amount, currency } = await request.json();

    // Validate required fields
    if (!chatId || !senderId || !text) {
      return NextResponse.json(
        { error: 'Chat ID, sender ID, and message text are required' },
        { status: 400 }
      );
    }

    // Validate message length
    if (text.length > 1000) {
      return NextResponse.json(
        { error: 'Message too long. Maximum 1000 characters.' },
        { status: 400 }
      );
    }

    // Validate payment message
    if (type === 'payment') {
      if (!amount || amount <= 0) {
        return NextResponse.json(
          { error: 'Valid amount is required for payment messages' },
          { status: 400 }
        );
      }
      if (amount > 10000) {
        return NextResponse.json(
          { error: 'Payment amount exceeds maximum limit' },
          { status: 400 }
        );
      }
    }

    // Verify chat exists and user is participant
    const chat = await firestoreService.getChat(chatId);
    if (!chat) {
      return NextResponse.json(
        { error: 'Chat not found' },
        { status: 404 }
      );
    }

    if (!chat.participants.includes(senderId)) {
      return NextResponse.json(
        { error: 'User is not a participant in this chat' },
        { status: 403 }
      );
    }

    // Send message
    const messageId = await firestoreService.sendMessage({
      chatId,
      senderId,
      text,
      type,
      amount,
      currency: currency || 'USD'
    });

    return NextResponse.json({
      success: true,
      messageId,
      message: 'Message sent successfully'
    });

  } catch (error: any) {
    console.error('Send message error:', error);
    
    return NextResponse.json(
      { error: error.message || 'Failed to send message' },
      { status: 500 }
    );
  }
}
