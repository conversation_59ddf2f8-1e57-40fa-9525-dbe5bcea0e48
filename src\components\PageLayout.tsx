import Navigation from './Navigation';
import Footer from './Footer';

interface PageLayoutProps {
  children: React.ReactNode;
  showNavigation?: boolean;
  showFooter?: boolean;
}

export default function PageLayout({ 
  children, 
  showNavigation = true, 
  showFooter = true 
}: PageLayoutProps) {
  return (
    <>
      <style jsx global>{`
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');

        body {
          background: linear-gradient(135deg, #111827 0%, #1F2937 50%, #374151 100%);
          font-family: 'Montserrat', sans-serif;
        }

        .gold-gradient {
          background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
        }

        .gold-border {
          border: 2px solid transparent;
          background: linear-gradient(#2D1B4E, #2D1B4E) padding-box,
                      linear-gradient(90deg, #D4AF37, #F2D675) border-box;
        }

        .feature-card {
          transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
        }

        .btn-hover:hover {
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);
        }

        .hero-pattern {
          background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .professional-shadow {
          box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3), 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .glass-card {
          background: linear-gradient(135deg, rgba(17, 24, 39, 0.8) 0%, rgba(31, 41, 55, 0.8) 100%);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(75, 85, 99, 0.3);
        }
      `}</style>

      <div className="text-white min-h-screen hero-pattern bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
        {showNavigation && <Navigation />}
        <main className={showNavigation ? "pt-20" : ""}>
          {children}
        </main>
        {showFooter && <Footer />}
      </div>
    </>
  );
}
