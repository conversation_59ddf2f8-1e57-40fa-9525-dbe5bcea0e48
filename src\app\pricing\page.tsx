'use client';

import Link from 'next/link';
import PageLayout from '@/components/PageLayout';

export default function PricingPage() {
  return (
    <PageLayout>
          </div>
        </nav>

        {/* Hero Section */}
        <section className="pt-24 pb-16">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h1 className="text-5xl lg:text-6xl font-bold mb-6 gold-gradient">Pricing</h1>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Choose the perfect plan for your communication needs. All plans include end-to-end encryption and secure money transfers.
              </p>
            </div>
          </div>
        </section>

        {/* Pricing Plans */}
        <section className="py-20 bg-purple-900">
          <div className="container mx-auto px-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
              
              {/* Free Plan */}
              <div className="bg-purple-800 rounded-2xl p-8 text-center">
                <h3 className="text-2xl font-bold mb-4">Free</h3>
                <div className="text-4xl font-bold mb-6 gold-gradient">$0</div>
                <p className="text-gray-300 mb-8">Perfect for personal use</p>
                
                <ul className="space-y-4 mb-8 text-left">
                  <li className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span>Unlimited messaging</span>
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span>Voice & video calls</span>
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span>End-to-end encryption</span>
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span>Money transfers up to $100/month</span>
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span>Group chats (up to 10 people)</span>
                  </li>
                </ul>
                
                <Link href="/auth" className="w-full bg-gray-700 hover:bg-gray-600 text-white px-6 py-3 rounded-full font-semibold transition-all inline-block">
                  Get Started
                </Link>
              </div>

              {/* Pro Plan */}
              <div className="bg-gradient-to-b from-yellow-400 to-yellow-500 rounded-2xl p-8 text-center text-gray-900 relative">
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-purple-600 text-white px-4 py-2 rounded-full text-sm font-semibold">
                  Most Popular
                </div>
                <h3 className="text-2xl font-bold mb-4">Pro</h3>
                <div className="text-4xl font-bold mb-6">$9.99</div>
                <p className="text-gray-700 mb-8">For power users and small teams</p>
                
                <ul className="space-y-4 mb-8 text-left">
                  <li className="flex items-center">
                    <i className="fas fa-check text-purple-600 mr-3"></i>
                    <span>Everything in Free</span>
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-purple-600 mr-3"></i>
                    <span>Money transfers up to $5,000/month</span>
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-purple-600 mr-3"></i>
                    <span>Group chats (up to 100 people)</span>
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-purple-600 mr-3"></i>
                    <span>Priority customer support</span>
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-purple-600 mr-3"></i>
                    <span>Advanced security features</span>
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-purple-600 mr-3"></i>
                    <span>File sharing up to 100MB</span>
                  </li>
                </ul>
                
                <Link href="/auth" className="w-full bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-full font-semibold transition-all inline-block">
                  Start Free Trial
                </Link>
              </div>

              {/* Business Plan */}
              <div className="bg-purple-800 rounded-2xl p-8 text-center">
                <h3 className="text-2xl font-bold mb-4">Business</h3>
                <div className="text-4xl font-bold mb-6 gold-gradient">$29.99</div>
                <p className="text-gray-300 mb-8">For businesses and organizations</p>
                
                <ul className="space-y-4 mb-8 text-left">
                  <li className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span>Everything in Pro</span>
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span>Unlimited money transfers</span>
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span>Unlimited group size</span>
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span>Admin controls & analytics</span>
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span>24/7 dedicated support</span>
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span>API access</span>
                  </li>
                </ul>
                
                <Link href="/contact" className="w-full gold-border bg-transparent px-6 py-3 rounded-full font-semibold transition-all hover:scale-105 inline-block">
                  <span className="text-yellow-400">Contact Sales</span>
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-20 bg-gray-900">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold mb-4">Frequently Asked Questions</h2>
              <div className="w-20 h-1 bg-yellow-400 mx-auto"></div>
            </div>
            
            <div className="max-w-4xl mx-auto space-y-6">
              <div className="bg-purple-800 rounded-xl p-6">
                <h3 className="text-xl font-semibold mb-3 text-yellow-400">Can I change my plan anytime?</h3>
                <p className="text-gray-300">Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.</p>
              </div>
              
              <div className="bg-purple-800 rounded-xl p-6">
                <h3 className="text-xl font-semibold mb-3 text-yellow-400">Are there any hidden fees?</h3>
                <p className="text-gray-300">No hidden fees. The price you see is what you pay. Money transfer fees may apply based on your bank.</p>
              </div>
              
              <div className="bg-purple-800 rounded-xl p-6">
                <h3 className="text-xl font-semibold mb-3 text-yellow-400">Is there a free trial?</h3>
                <p className="text-gray-300">Yes! Pro and Business plans come with a 14-day free trial. No credit card required.</p>
              </div>
              
              <div className="bg-purple-800 rounded-xl p-6">
                <h3 className="text-xl font-semibold mb-3 text-yellow-400">What payment methods do you accept?</h3>
                <p className="text-gray-300">We accept all major credit cards, PayPal, and bank transfers for Business plans.</p>
              </div>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-gray-900 py-12">
          <div className="container mx-auto px-6">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="mb-6 md:mb-0">
                <Link href="/" className="flex items-center">
                  <div className="text-yellow-400 text-2xl mr-2">
                    <i className="fas fa-comment-dollar"></i>
                  </div>
                  <span className="font-bold text-xl gold-gradient">BoGuani</span>
                </Link>
                <p className="text-gray-400 mt-2">Messenger of Value</p>
              </div>
              
              <div className="text-center">
                <p className="text-gray-400 text-sm">© 2024 BoGuani. All rights reserved.</p>
                <div className="flex space-x-6 text-gray-400 text-sm mt-2">
                  <Link href="/privacy" className="hover:text-yellow-400 transition-colors">Privacy Policy</Link>
                  <Link href="/terms" className="hover:text-yellow-400 transition-colors">Terms of Service</Link>
                  <Link href="/security" className="hover:text-yellow-400 transition-colors">Security</Link>
                </div>
              </div>
            </div>
          </div>
    </PageLayout>
  );
}
