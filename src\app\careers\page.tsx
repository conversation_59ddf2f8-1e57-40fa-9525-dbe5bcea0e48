'use client';

import Link from 'next/link';
import PageLayout from '@/components/PageLayout';

export default function CareersPage() {
  return (
    <>
      <style jsx global>{`
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');
        
        body {
          background: linear-gradient(135deg, #1E1E24 0%, #2D1B4E 100%);
          font-family: 'Montserrat', sans-serif;
        }
        
        .gold-gradient {
          background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
        }
        
        .gold-border {
          border: 2px solid transparent;
          background: linear-gradient(#2D1B4E, #2D1B4E) padding-box,
                      linear-gradient(90deg, #D4AF37, #F2D675) border-box;
        }
        
        .hero-pattern {
          background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
      `}</style>

      <div className="text-white min-h-screen hero-pattern">
        {/* Navigation */}
        <nav className="bg-gray-900 bg-opacity-80 backdrop-blur-md fixed w-full z-10">
          <div className="container mx-auto px-6 py-3 flex justify-between items-center">
            <Link href="/" className="flex items-center">
              <div className="text-yellow-400 text-3xl mr-2">
                <i className="fas fa-comment-dollar"></i>
              </div>
              <span className="font-bold text-2xl gold-gradient">BoGuani</span>
            </Link>
            <div className="hidden md:flex space-x-8">
              <Link href="/#features" className="hover:text-yellow-400 transition-colors">Features</Link>
              <Link href="/#about" className="hover:text-yellow-400 transition-colors">About</Link>
              <Link href="/#download" className="hover:text-yellow-400 transition-colors">Download</Link>
              <Link href="/support" className="hover:text-yellow-400 transition-colors">Support</Link>
            </div>
          </div>
        </nav>

        {/* Hero Section */}
        <section className="pt-24 pb-16">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h1 className="text-5xl lg:text-6xl font-bold mb-6 gold-gradient">Join Our Team</h1>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Help us build the future of value-based communication. Join a team that&apos;s revolutionizing how people connect and share value.
              </p>
            </div>
          </div>
        </section>

        {/* Company Values */}
        <section className="py-20 bg-purple-900">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold mb-4">Our Values</h2>
              <div className="w-20 h-1 bg-yellow-400 mx-auto"></div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="bg-purple-800 p-8 rounded-xl text-center">
                <div className="text-yellow-400 text-5xl mb-6">
                  <i className="fas fa-heart"></i>
                </div>
                <h3 className="text-xl font-semibold mb-4">Sacred Trust</h3>
                <p className="text-gray-300">We protect our users&apos; privacy and security with the same reverence ancient cultures held for sacred communication.</p>
              </div>
              
              <div className="bg-purple-800 p-8 rounded-xl text-center">
                <div className="text-yellow-400 text-5xl mb-6">
                  <i className="fas fa-lightbulb"></i>
                </div>
                <h3 className="text-xl font-semibold mb-4">Innovation</h3>
                <p className="text-gray-300">We constantly push boundaries to create meaningful connections between communication and value exchange.</p>
              </div>
              
              <div className="bg-purple-800 p-8 rounded-xl text-center">
                <div className="text-yellow-400 text-5xl mb-6">
                  <i className="fas fa-users"></i>
                </div>
                <h3 className="text-xl font-semibold mb-4">Community</h3>
                <p className="text-gray-300">We believe in building inclusive teams that reflect the diverse communities we serve worldwide.</p>
              </div>
            </div>
          </div>
        </section>

        {/* Open Positions */}
        <section className="py-20 bg-gray-900">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold mb-4">Open Positions</h2>
              <div className="w-20 h-1 bg-yellow-400 mx-auto"></div>
            </div>
            
            <div className="max-w-4xl mx-auto space-y-6">
              <div className="bg-purple-800 rounded-xl p-6 hover:bg-purple-700 transition-colors">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-xl font-semibold mb-2 text-yellow-400">Senior Full-Stack Developer</h3>
                    <p className="text-gray-300 mb-4">Help build our core messaging and payment infrastructure using React, Node.js, and blockchain technologies.</p>
                    <div className="flex flex-wrap gap-2">
                      <span className="bg-yellow-400/20 text-yellow-400 px-3 py-1 rounded-full text-sm">React</span>
                      <span className="bg-yellow-400/20 text-yellow-400 px-3 py-1 rounded-full text-sm">Node.js</span>
                      <span className="bg-yellow-400/20 text-yellow-400 px-3 py-1 rounded-full text-sm">TypeScript</span>
                      <span className="bg-yellow-400/20 text-yellow-400 px-3 py-1 rounded-full text-sm">WebRTC</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-gray-400 text-sm">Remote</p>
                    <p className="text-gray-400 text-sm">Full-time</p>
                  </div>
                </div>
              </div>
              
              <div className="bg-purple-800 rounded-xl p-6 hover:bg-purple-700 transition-colors">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-xl font-semibold mb-2 text-yellow-400">Security Engineer</h3>
                    <p className="text-gray-300 mb-4">Lead our security initiatives, implement end-to-end encryption, and ensure the highest standards of user privacy.</p>
                    <div className="flex flex-wrap gap-2">
                      <span className="bg-yellow-400/20 text-yellow-400 px-3 py-1 rounded-full text-sm">Cryptography</span>
                      <span className="bg-yellow-400/20 text-yellow-400 px-3 py-1 rounded-full text-sm">Security</span>
                      <span className="bg-yellow-400/20 text-yellow-400 px-3 py-1 rounded-full text-sm">Penetration Testing</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-gray-400 text-sm">San Francisco</p>
                    <p className="text-gray-400 text-sm">Full-time</p>
                  </div>
                </div>
              </div>
              
              <div className="bg-purple-800 rounded-xl p-6 hover:bg-purple-700 transition-colors">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-xl font-semibold mb-2 text-yellow-400">Product Designer</h3>
                    <p className="text-gray-300 mb-4">Design intuitive user experiences that make complex financial communications feel natural and beautiful.</p>
                    <div className="flex flex-wrap gap-2">
                      <span className="bg-yellow-400/20 text-yellow-400 px-3 py-1 rounded-full text-sm">UI/UX</span>
                      <span className="bg-yellow-400/20 text-yellow-400 px-3 py-1 rounded-full text-sm">Figma</span>
                      <span className="bg-yellow-400/20 text-yellow-400 px-3 py-1 rounded-full text-sm">Mobile Design</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-gray-400 text-sm">Remote</p>
                    <p className="text-gray-400 text-sm">Full-time</p>
                  </div>
                </div>
              </div>
              
              <div className="bg-purple-800 rounded-xl p-6 hover:bg-purple-700 transition-colors">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-xl font-semibold mb-2 text-yellow-400">DevOps Engineer</h3>
                    <p className="text-gray-300 mb-4">Scale our infrastructure to handle millions of secure messages and transactions worldwide.</p>
                    <div className="flex flex-wrap gap-2">
                      <span className="bg-yellow-400/20 text-yellow-400 px-3 py-1 rounded-full text-sm">AWS</span>
                      <span className="bg-yellow-400/20 text-yellow-400 px-3 py-1 rounded-full text-sm">Kubernetes</span>
                      <span className="bg-yellow-400/20 text-yellow-400 px-3 py-1 rounded-full text-sm">Docker</span>
                      <span className="bg-yellow-400/20 text-yellow-400 px-3 py-1 rounded-full text-sm">Terraform</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-gray-400 text-sm">Remote</p>
                    <p className="text-gray-400 text-sm">Full-time</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Benefits */}
        <section className="py-20 bg-purple-900">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold mb-4">Why Work With Us</h2>
              <div className="w-20 h-1 bg-yellow-400 mx-auto"></div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="text-yellow-400 text-4xl mb-4">
                  <i className="fas fa-home"></i>
                </div>
                <h3 className="font-semibold mb-2">Remote First</h3>
                <p className="text-gray-400 text-sm">Work from anywhere in the world</p>
              </div>
              
              <div className="text-center">
                <div className="text-yellow-400 text-4xl mb-4">
                  <i className="fas fa-heart"></i>
                </div>
                <h3 className="font-semibold mb-2">Health & Wellness</h3>
                <p className="text-gray-400 text-sm">Comprehensive health coverage</p>
              </div>
              
              <div className="text-center">
                <div className="text-yellow-400 text-4xl mb-4">
                  <i className="fas fa-graduation-cap"></i>
                </div>
                <h3 className="font-semibold mb-2">Learning Budget</h3>
                <p className="text-gray-400 text-sm">$2,000 annual learning allowance</p>
              </div>
              
              <div className="text-center">
                <div className="text-yellow-400 text-4xl mb-4">
                  <i className="fas fa-chart-line"></i>
                </div>
                <h3 className="font-semibold mb-2">Equity Package</h3>
                <p className="text-gray-400 text-sm">Share in our success</p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-b from-purple-900 to-gray-900">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to Join the Revolution?</h2>
            <p className="text-xl text-gray-300 mb-10 max-w-2xl mx-auto">Don&apos;t see a position that fits? We&apos;re always looking for exceptional talent.</p>
            
            <div className="flex flex-wrap justify-center gap-6">
              <Link href="/contact" className="bg-yellow-400 text-gray-900 px-8 py-4 rounded-full font-semibold transition-all hover:scale-105 flex items-center">
                <i className="fas fa-envelope mr-2"></i> Send Us Your Resume
              </Link>
              <Link href="/support" className="gold-border bg-transparent px-8 py-4 rounded-full font-semibold transition-all hover:scale-105 flex items-center">
                <i className="fas fa-question-circle mr-2 text-yellow-400"></i> <span className="text-yellow-400">Have Questions?</span>
              </Link>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-gray-900 py-12">
          <div className="container mx-auto px-6">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="mb-6 md:mb-0">
                <Link href="/" className="flex items-center">
                  <div className="text-yellow-400 text-2xl mr-2">
                    <i className="fas fa-comment-dollar"></i>
                  </div>
                  <span className="font-bold text-xl gold-gradient">BoGuani</span>
                </Link>
                <p className="text-gray-400 mt-2">Messenger of Value</p>
              </div>
              
              <div className="text-center">
                <p className="text-gray-400 text-sm">© 2024 BoGuani. All rights reserved.</p>
                <div className="flex space-x-6 text-gray-400 text-sm mt-2">
                  <Link href="/privacy" className="hover:text-yellow-400 transition-colors">Privacy Policy</Link>
                  <Link href="/terms" className="hover:text-yellow-400 transition-colors">Terms of Service</Link>
                  <Link href="/security" className="hover:text-yellow-400 transition-colors">Security</Link>
                </div>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
